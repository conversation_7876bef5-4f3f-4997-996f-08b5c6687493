#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Email Debug Script - Test different email methods
"""

import subprocess
import sys
import os
from datetime import datetime

# Add the script directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

def test_sendmail_direct():
    """Test sending email directly via sendmail command"""
    print("🧪 Testing direct sendmail...")
    
    email_content = f"""To: <EMAIL>
From: <EMAIL>
Subject: 🧪 Direct Sendmail Test - Istanbul Pharmacy Scraper

Direct Sendmail Test

📅 Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
📧 This email was sent directly using the sendmail command.

If you receive this email, the sendmail method is working!

---
Istanbul Pharmacy Scraper System
istanbulnobetcieczane.com"""

    try:
        # Use sendmail command directly
        process = subprocess.Popen(['/usr/sbin/sendmail', '-t'], stdin=subprocess.PIPE, text=True)
        stdout, stderr = process.communicate(input=email_content)
        
        if process.returncode == 0:
            print("✅ Sendmail command executed successfully!")
            return True
        else:
            print(f"❌ Sendmail failed with return code: {process.returncode}")
            if stderr:
                print(f"Error: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Sendmail test failed: {e}")
        return False

def test_mail_command():
    """Test using the mail command"""
    print("🧪 Testing mail command...")
    
    try:
        # Check if mail command exists
        result = subprocess.run(['which', 'mail'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Mail command not found")
            return False
        
        # Send email using mail command
        subject = "🧪 Mail Command Test - Istanbul Pharmacy Scraper"
        message = f"""Mail Command Test

📅 Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
📧 This email was sent using the mail command.

If you receive this email, the mail command method is working!

---
Istanbul Pharmacy Scraper System
istanbulnobetcieczane.com"""

        process = subprocess.Popen(
            ['mail', '-s', subject, '<EMAIL>'],
            stdin=subprocess.PIPE,
            text=True
        )
        stdout, stderr = process.communicate(input=message)
        
        if process.returncode == 0:
            print("✅ Mail command executed successfully!")
            return True
        else:
            print(f"❌ Mail command failed with return code: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Mail command test failed: {e}")
        return False

def check_mail_logs():
    """Check recent mail logs"""
    print("📋 Checking mail logs...")
    
    log_files = ['/var/log/mail.log', '/var/log/maillog', '/var/log/messages']
    
    for log_file in log_files:
        try:
            if os.path.exists(log_file):
                print(f"\n📄 Recent entries from {log_file}:")
                result = subprocess.run(['tail', '-10', log_file], capture_output=True, text=True)
                if result.returncode == 0:
                    print(result.stdout)
                break
        except Exception as e:
            continue
    else:
        print("❌ No mail logs found")

def check_mail_queue():
    """Check mail queue"""
    print("📬 Checking mail queue...")
    
    try:
        # Check mailq
        result = subprocess.run(['mailq'], capture_output=True, text=True)
        if result.returncode == 0:
            print("Mail queue status:")
            print(result.stdout)
        else:
            print("❌ Could not check mail queue")
    except Exception as e:
        print(f"❌ Error checking mail queue: {e}")

def main():
    print("🔧 Email Debug and Test Script")
    print("=" * 50)
    
    # Test 1: Direct sendmail
    print("\n1️⃣ Testing direct sendmail method...")
    sendmail_success = test_sendmail_direct()
    
    # Test 2: Mail command
    print("\n2️⃣ Testing mail command method...")
    mail_success = test_mail_command()
    
    # Check logs
    print("\n3️⃣ Checking mail system...")
    check_mail_logs()
    check_mail_queue()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"  Sendmail: {'✅ Success' if sendmail_success else '❌ Failed'}")
    print(f"  Mail command: {'✅ Success' if mail_success else '❌ Failed'}")
    
    if sendmail_success or mail_success:
        print("\n✅ At least one email method is working!")
        print("📧 Check your email (<EMAIL>) in a few minutes.")
        print("📝 Note: Emails might take time to be delivered or could go to spam.")
    else:
        print("\n❌ No email methods are working properly.")
        print("🔧 Server mail configuration may need adjustment.")
    
    print("\n💡 Alternative Solutions:")
    print("1. Set up email forwarding in cPanel")
    print("2. Use external SMTP (Gmail, SendGrid, etc.)")
    print("3. Configure server mail properly")
    print("4. Use webhook notifications instead")

if __name__ == "__main__":
    main()
