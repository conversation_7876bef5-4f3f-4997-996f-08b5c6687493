#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration file for Turkish Pharmacy Duty Scraper
"""

import os

# Database Configuration
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'database': 'sgkoduyormu_wp_w8bl5',  # Using the WordPress database
    'user': 'sgkoduyormu_wp_ku1ca',
    'password': '7vfQ31#j0h!~qq8C',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci',
    'autocommit': True,
    'raise_on_warnings': True
}

# Scraper Configuration
SCRAPER_CONFIG = {
    'base_url': 'https://www.turkiye.gov.tr',
    'search_endpoint': '/saglik-titck-nobetci-eczane-sorgulama',
    'request_timeout': 30,
    'retry_attempts': 3,
    'retry_delay': 5,  # seconds
    'rate_limit_delay': 2,  # seconds between requests
    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
}

# WordPress Integration
WORDPRESS_CONFIG = {
    'table_prefix': 'rJqwdO1B6_',  # From wp-config.php
    'pharmacy_table': 'duty_pharmacies',
    'log_table': 'scraper_logs'
}

# Logging Configuration
LOGGING_CONFIG = {
    'log_file': 'scraper.log',
    'log_level': 'INFO',
    'max_log_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# Email Notifications (optional)
EMAIL_CONFIG = {
    'enabled': True,
    'smtp_server': 'localhost',
    'smtp_port': 25,  # Try port 25 for local delivery
    'username': '<EMAIL>',
    'password': 'ia.3d_bu7C2A',
    'from_email': '<EMAIL>',
    'to_emails': ['<EMAIL>'],
    'send_on_error': True,
    'send_daily_summary': True,
    # Alternative SMTP configurations (uncomment to use)
    # Gmail SMTP (requires app password):
    # 'smtp_server': 'smtp.gmail.com',
    # 'smtp_port': 587,
    # 'username': '<EMAIL>',
    # 'password': 'your-app-password',
    # 'from_email': '<EMAIL>',
}

# Turkish Cities with their codes (if needed for API calls)
TURKISH_CITIES = {
    'ADANA': '01',
    'ADIYAMAN': '02',
    'AFYONKARAHİSAR': '03',
    'AĞRI': '04',
    'AKSARAY': '68',
    'AMASYA': '05',
    'ANKARA': '06',
    'ANTALYA': '07',
    'ARDAHAN': '75',
    'ARTVİN': '08',
    'AYDIN': '09',
    'BALIKESİR': '10',
    'BARTIN': '74',
    'BATMAN': '72',
    'BAYBURT': '69',
    'BİLECİK': '11',
    'BİNGÖL': '12',
    'BİTLİS': '13',
    'BOLU': '14',
    'BURDUR': '15',
    'BURSA': '16',
    'ÇANAKKALE': '17',
    'ÇANKIRI': '18',
    'ÇORUM': '19',
    'DENİZLİ': '20',
    'DİYARBAKIR': '21',
    'DÜZCE': '81',
    'EDİRNE': '22',
    'ELAZIĞ': '23',
    'ERZİNCAN': '24',
    'ERZURUM': '25',
    'ESKİŞEHİR': '26',
    'GAZİANTEP': '27',
    'GİRESUN': '28',
    'GÜMÜŞHANE': '29',
    'HAKKARİ': '30',
    'HATAY': '31',
    'IĞDIR': '76',
    'ISPARTA': '32',
    'İSTANBUL': '34',
    'İZMİR': '35',
    'KAHRAMANMARAŞ': '46',
    'KARABÜK': '78',
    'KARAMAN': '70',
    'KARS': '36',
    'KASTAMONU': '37',
    'KAYSERİ': '38',
    'KIRIKKALE': '71',
    'KIRKLARELİ': '39',
    'KIRŞEHİR': '40',
    'KİLİS': '79',
    'KOCAELİ': '41',
    'KONYA': '42',
    'KÜTAHYA': '43',
    'MALATYA': '44',
    'MANİSA': '45',
    'MARDİN': '47',
    'MERSİN': '33',
    'MUĞLA': '48',
    'MUŞ': '49',
    'NEVŞEHİR': '50',
    'NİĞDE': '51',
    'ORDU': '52',
    'OSMANİYE': '80',
    'RİZE': '53',
    'SAKARYA': '54',
    'SAMSUN': '55',
    'SİİRT': '56',
    'SİNOP': '57',
    'SİVAS': '58',
    'ŞANLIURFA': '63',
    'ŞIRNAK': '73',
    'TEKİRDAĞ': '59',
    'TOKAT': '60',
    'TRABZON': '61',
    'TUNCELİ': '62',
    'UŞAK': '64',
    'VAN': '65',
    'YALOVA': '77',
    'YOZGAT': '66',
    'ZONGULDAK': '67'
}

# Priority cities for scraping (can be used to prioritize certain cities)
PRIORITY_CITIES = [
    'İSTANBUL',
    'ANKARA',
    'İZMİR',
    'BURSA',
    'ANTALYA',
    'ADANA',
    'KONYA',
    'GAZİANTEP',
    'MERSIN',
    'DİYARBAKIR'
]

# Cron schedule settings
CRON_CONFIG = {
    'daily_scrape_hour': 6,  # 6 AM
    'daily_scrape_minute': 0,
    'cleanup_old_data_days': 30,  # Keep data for 30 days
    'backup_frequency_days': 7  # Backup every 7 days
}

# API endpoints (if the site uses AJAX calls)
API_ENDPOINTS = {
    'get_districts': '/api/get-districts',
    'search_pharmacies': '/api/search-pharmacies',
    'get_pharmacy_details': '/api/pharmacy-details'
}

# Error handling
ERROR_CONFIG = {
    'max_retries': 3,
    'retry_delay': 5,
    'timeout': 30,
    'ignore_ssl_errors': False,
    'continue_on_error': True
}

# Data validation rules
VALIDATION_RULES = {
    'required_fields': ['name', 'city', 'duty_date'],
    'max_name_length': 255,
    'max_address_length': 500,
    'phone_pattern': r'^[\d\s\-\(\)\+]+$',
    'date_format': '%d/%m/%Y'
}
