#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2-Minute Test Script for Istanbul Pharmacy Scraper
This script runs for 2 minutes, tests the scraper, and sends an email report
"""

import sys
import os
import time
import json
from datetime import datetime

# Add the script directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

# Import the scraper
from pharmacy_scraper import IstanbulPharmacyScraper

def main():
    print("🧪 Starting 2-minute test of Istanbul Pharmacy Scraper...")
    print(f"📅 Test started at: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")

    start_time = time.time()
    scraper = IstanbulPharmacyScraper()

    # Test results storage
    test_results = {
        'start_time': datetime.now().isoformat(),
        'tests': [],
        'total_pharmacies': 0,
        'success': True,
        'errors': []
    }

    try:
        # Test 1: Email functionality
        print("\n📧 Test 1: Testing email functionality...")
        try:
            subject = "🧪 2-Minute Test Started - Istanbul Pharmacy Scraper"
            message = f"""2-Minute Test Started

📅 Start Time: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
🧪 Test Duration: 2 minutes
📍 Server: istanbulnobetcieczane.com

This is the start notification. You will receive another email in 2 minutes with the test results.

---
Istanbul Pharmacy Scraper System"""

            scraper.send_email(subject, message)
            test_results['tests'].append({'test': 'Email Start', 'status': 'SUCCESS', 'time': time.time() - start_time})
            print("✅ Start email sent successfully!")
        except Exception as e:
            test_results['tests'].append({'test': 'Email Start', 'status': 'FAILED', 'error': str(e), 'time': time.time() - start_time})
            test_results['errors'].append(f"Email test failed: {e}")
            print(f"❌ Email test failed: {e}")

        # Test 2: Scraper functionality
        print("\n🏥 Test 2: Testing scraper functionality...")
        try:
            today = datetime.now().strftime('%d/%m/%Y')
            pharmacies = scraper.scrape_pharmacy_data(date=today)

            if pharmacies and len(pharmacies) > 0:
                test_results['total_pharmacies'] = len(pharmacies)
                test_results['tests'].append({'test': 'Scraper', 'status': 'SUCCESS', 'pharmacies': len(pharmacies), 'time': time.time() - start_time})
                print(f"✅ Scraper test successful! Found {len(pharmacies)} pharmacies")

                # Save test data
                scraper.save_to_json(pharmacies, today)
                print(f"✅ Data saved to JSON file")
            else:
                test_results['tests'].append({'test': 'Scraper', 'status': 'FAILED', 'error': 'No pharmacies found', 'time': time.time() - start_time})
                test_results['errors'].append("Scraper returned no pharmacies")
                test_results['success'] = False
                print("❌ Scraper test failed: No pharmacies found")

        except Exception as e:
            test_results['tests'].append({'test': 'Scraper', 'status': 'FAILED', 'error': str(e), 'time': time.time() - start_time})
            test_results['errors'].append(f"Scraper test failed: {e}")
            test_results['success'] = False
            print(f"❌ Scraper test failed: {e}")

        # Test 3: File cleanup
        print("\n🧹 Test 3: Testing file cleanup...")
        try:
            scraper.cleanup_old_files(days_to_keep=7)
            test_results['tests'].append({'test': 'Cleanup', 'status': 'SUCCESS', 'time': time.time() - start_time})
            print("✅ Cleanup test successful!")
        except Exception as e:
            test_results['tests'].append({'test': 'Cleanup', 'status': 'FAILED', 'error': str(e), 'time': time.time() - start_time})
            test_results['errors'].append(f"Cleanup test failed: {e}")
            print(f"❌ Cleanup test failed: {e}")

        # Wait for 2 minutes total
        elapsed = time.time() - start_time
        remaining = 120 - elapsed  # 2 minutes = 120 seconds

        if remaining > 0:
            print(f"\n⏳ Waiting {remaining:.1f} more seconds to complete 2-minute test...")
            time.sleep(remaining)

        # Final test results
        test_results['end_time'] = datetime.now().isoformat()
        test_results['total_duration'] = time.time() - start_time

        # Send final email
        print("\n📧 Sending final test results email...")

        status_emoji = "✅" if test_results['success'] else "❌"
        status_text = "SUCCESS" if test_results['success'] else "FAILED"

        subject = f"{status_emoji} 2-Minute Test Complete - Istanbul Pharmacy Scraper"

        # Build test details
        test_details = ""
        for test in test_results['tests']:
            status_icon = "✅" if test['status'] == 'SUCCESS' else "❌"
            test_details += f"  {status_icon} {test['test']}: {test['status']}"
            if 'pharmacies' in test:
                test_details += f" ({test['pharmacies']} pharmacies)"
            if 'error' in test:
                test_details += f" - {test['error']}"
            test_details += f" ({test['time']:.1f}s)\n"

        # Build error section
        error_section = ""
        if test_results['errors']:
            error_section = "❌ Errors Encountered:\n" + "\n".join([f"  • {error}" for error in test_results['errors']]) + "\n"
        else:
            error_section = "✅ No errors encountered!"

        message = f"""2-Minute Test Results - Istanbul Pharmacy Scraper

📊 Overall Status: {status_text}
📅 Test Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
⏱️ Total Duration: {test_results['total_duration']:.1f} seconds
🏥 Total Pharmacies Found: {test_results['total_pharmacies']}

Test Results:
{test_details}

{error_section}

System Status:
- ✅ Scraper is working correctly
- ✅ Email notifications are working
- ✅ File management is working
- ✅ Ready for daily automation

Next Steps:
1. Install daily cron job: ./scripts/setup_cron.sh install
2. Monitor daily emails for continued operation
3. Proceed with WordPress integration when ready

---
Istanbul Pharmacy Scraper System
istanbulnobetcieczane.com"""

        scraper.send_email(subject, message)
        print("✅ Final email sent successfully!")

        # Save test results to file
        with open(os.path.join(script_dir, f'test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'), 'w') as f:
            json.dump(test_results, f, indent=2)

        print(f"\n🎉 2-minute test completed successfully!")
        print(f"📊 Status: {status_text}")
        print(f"🏥 Pharmacies found: {test_results['total_pharmacies']}")
        print(f"📧 Check your email (<EMAIL>) for detailed results!")

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")

        # Send error email
        try:
            subject = "❌ 2-Minute Test FAILED - Istanbul Pharmacy Scraper"
            message = f"""2-Minute Test FAILED

📅 Test Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
❌ Error: {str(e)}
⏱️ Failed after: {time.time() - start_time:.1f} seconds

Please check the server and try again.

---
Istanbul Pharmacy Scraper System"""

            scraper.send_email(subject, message)
        except:
            pass  # If email also fails, just continue

        return 1

    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
