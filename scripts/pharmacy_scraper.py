#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Turkish Pharmacy Duty Scraper - Istanbul Focus
Scrapes duty pharmacy information from turkiye.gov.tr for Istanbul only
Saves data as JSON files for today and tomorrow
"""

import requests
import json
import logging
import sys
import os
import glob
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON>art
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import time
import re

# Setup logging
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

# Import configuration
try:
    from config import EMAIL_CONFIG
except ImportError:
    EMAIL_CONFIG = {'enabled': False}
    print("Warning: config.py not found. Email notifications disabled.")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(script_dir, 'scraper.log')),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class IstanbulPharmacyScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'tr-TR,tr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        self.base_url = "https://www.turkiye.gov.tr"
        self.search_url = f"{self.base_url}/saglik-titck-nobetci-eczane-sorgulama"
        self.city = "İSTANBUL"

    def get_token(self):
        """Get token from the main page"""
        try:
            response = self.session.get(self.search_url)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Look for the token field in the form
            token_input = soup.find('input', {'name': 'token'})
            if token_input:
                return token_input.get('value')

            # Alternative: look for data-token in body
            body = soup.find('body')
            if body and body.get('data-token'):
                return body.get('data-token')

            logger.warning("Token not found")
            return None

        except Exception as e:
            logger.error(f"Error getting token: {e}")
            return None

    def scrape_pharmacy_data(self, date=None):
        """Scrape pharmacy data for Istanbul on given date"""
        if date is None:
            date = datetime.now().strftime('%d/%m/%Y')

        try:
            logger.info(f"Scraping Istanbul pharmacies for {date}")

            # Get token
            token = self.get_token()

            # Prepare form data with correct field names
            form_data = {
                'plakaKodu': '34',  # Istanbul's plate code
                'nobetTarihi': date,
                'btn': 'Sorgula'
            }

            if token:
                form_data['token'] = token

            # Make POST request to the submit URL
            submit_url = f"{self.search_url}?submit"
            response = self.session.post(
                submit_url,
                data=form_data,
                timeout=30
            )
            response.raise_for_status()

            # Parse response
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract pharmacy data - we need to inspect the actual HTML structure
            pharmacies = []

            # Let's first save the raw HTML to understand the structure
            debug_filename = f'debug_response_submit_{date.replace("/", "_")}.html'
            with open(debug_filename, 'w', encoding='utf-8') as f:
                f.write(response.text)

            logger.info(f"Saved debug response to {debug_filename}")

            # Look for the results table
            results_table = soup.find('table', {'id': 'searchTable'})

            if results_table:
                logger.info("Found results table")

                # Find all table rows in tbody
                tbody = results_table.find('tbody')
                if tbody:
                    rows = tbody.find_all('tr')
                    logger.info(f"Found {len(rows)} pharmacy rows in table")

                    for row in rows:
                        pharmacy_data = self.extract_pharmacy_from_table_row(row)
                        if pharmacy_data:
                            pharmacy_data['city'] = self.city
                            pharmacy_data['duty_date'] = date
                            pharmacy_data['scraped_at'] = datetime.now().isoformat()
                            pharmacies.append(pharmacy_data)
                else:
                    logger.warning("Table found but no tbody")
            else:
                logger.warning("No results table found")

                # Fallback: check for any text containing "eczane"
                all_text = soup.get_text()
                if 'eczane' in all_text.lower():
                    logger.info("Found 'eczane' text in response, trying fallback extraction")

                    # Try to extract pharmacy info from text blocks
                    text_blocks = soup.find_all(['p', 'div', 'span', 'td'])
                    for block in text_blocks:
                        block_text = block.get_text(strip=True)
                        if 'eczane' in block_text.lower() and len(block_text) > 10:
                            pharmacy_data = self.extract_pharmacy_info(block)
                            if pharmacy_data:
                                pharmacy_data['city'] = self.city
                                pharmacy_data['duty_date'] = date
                                pharmacy_data['scraped_at'] = datetime.now().isoformat()
                                pharmacies.append(pharmacy_data)

            # Remove duplicates based on name
            unique_pharmacies = []
            seen_names = set()
            for pharmacy in pharmacies:
                if pharmacy['name'] and pharmacy['name'] not in seen_names:
                    unique_pharmacies.append(pharmacy)
                    seen_names.add(pharmacy['name'])

            logger.info(f"Successfully extracted {len(unique_pharmacies)} unique pharmacies for Istanbul on {date}")
            return unique_pharmacies

        except Exception as e:
            logger.error(f"Error scraping data for Istanbul on {date}: {e}")
            return []

    def extract_pharmacy_from_table_row(self, row):
        """Extract pharmacy information from table row"""
        try:
            cells = row.find_all('td')
            if len(cells) < 4:
                return None

            # Table structure: Name, District, Phone, Address, Action
            pharmacy_data = {
                'name': '',
                'address': '',
                'phone': '',
                'district': '',
                'latitude': None,
                'longitude': None
            }

            # Extract name (first column)
            name_cell = cells[0]
            pharmacy_data['name'] = name_cell.get_text(strip=True)

            # Extract district (second column)
            district_cell = cells[1]
            pharmacy_data['district'] = district_cell.get_text(strip=True)

            # Extract phone (third column)
            phone_cell = cells[2]
            phone_text = phone_cell.get_text(strip=True)
            # Clean up phone number - remove extra spaces and format
            phone_clean = re.sub(r'\s+', ' ', phone_text).strip()
            # Extract just the number part
            phone_match = re.search(r'(\d[\d\s\-\(\)]+\d)', phone_clean)
            if phone_match:
                pharmacy_data['phone'] = phone_match.group(1).strip()
            else:
                pharmacy_data['phone'] = phone_clean

            # Extract address (fourth column)
            address_cell = cells[3]
            pharmacy_data['address'] = address_cell.get_text(strip=True)

            # Only return if we have at least a name
            return pharmacy_data if pharmacy_data['name'] else None

        except Exception as e:
            logger.error(f"Error extracting pharmacy from table row: {e}")
            return None

    def extract_pharmacy_info(self, element):
        """Extract pharmacy information from HTML element"""
        try:
            # Initialize pharmacy data structure
            pharmacy_data = {
                'name': '',
                'address': '',
                'phone': '',
                'district': '',
                'latitude': None,
                'longitude': None
            }

            # Get all text from the element
            element_text = element.get_text(strip=True)

            # Skip empty elements or navigation elements
            if not element_text or len(element_text) < 10:
                return None

            # Skip common non-pharmacy elements
            skip_keywords = ['menü', 'menu', 'sayfa', 'giriş', 'çıkış', 'arama', 'seçiniz']
            if any(keyword in element_text.lower() for keyword in skip_keywords):
                return None

            # Try different selectors for name
            name_elem = (
                element.find('h1') or element.find('h2') or element.find('h3') or
                element.find('strong') or element.find('b') or
                element.find('td') or element.find('span')
            )

            if name_elem:
                name_text = name_elem.get_text(strip=True)
                if name_text and 'eczane' in name_text.lower():
                    pharmacy_data['name'] = name_text

            # If no name found but element contains "eczane", use the whole text
            if not pharmacy_data['name'] and 'eczane' in element_text.lower():
                # Split by common separators and take the first part as name
                lines = element_text.split('\n')
                for line in lines:
                    if 'eczane' in line.lower() and len(line.strip()) > 5:
                        pharmacy_data['name'] = line.strip()
                        break

            # Extract address (usually longer text)
            address_elem = element.find('p') or element.find('div')
            if address_elem:
                address_text = address_elem.get_text(strip=True)
                if len(address_text) > 20 and address_text != pharmacy_data['name']:
                    pharmacy_data['address'] = address_text

            # Extract phone (look for number patterns)
            phone_pattern = r'(\d{3}[-.\s]?\d{3}[-.\s]?\d{2}[-.\s]?\d{2}|\d{3}[-.\s]?\d{3}[-.\s]?\d{4})'
            phone_match = re.search(phone_pattern, element_text)
            if phone_match:
                pharmacy_data['phone'] = phone_match.group(1)

            # Return data only if we found a name
            return pharmacy_data if pharmacy_data['name'] else None

        except Exception as e:
            logger.error(f"Error extracting pharmacy info: {e}")
            return None

    def save_to_json(self, pharmacies, date):
        """Save pharmacy data to JSON file"""
        if not pharmacies:
            logger.warning(f"No pharmacy data to save for {date}")
            return

        try:
            # Create filename based on date
            filename = f"istanbul_pharmacies_{date.replace('/', '_')}.json"
            filepath = os.path.join(script_dir, filename)

            # Prepare data structure
            data = {
                'city': self.city,
                'date': date,
                'scraped_at': datetime.now().isoformat(),
                'total_pharmacies': len(pharmacies),
                'pharmacies': pharmacies
            }

            # Save to JSON file
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info(f"Saved {len(pharmacies)} pharmacies to {filename}")

        except Exception as e:
            logger.error(f"Error saving to JSON: {e}")

    def cleanup_old_files(self, days_to_keep=7):
        """Remove JSON files older than specified days"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            # Find all Istanbul pharmacy JSON files
            pattern = os.path.join(script_dir, "istanbul_pharmacies_*.json")
            json_files = glob.glob(pattern)

            deleted_count = 0
            for file_path in json_files:
                try:
                    # Extract date from filename (format: istanbul_pharmacies_DD_MM_YYYY.json)
                    filename = os.path.basename(file_path)
                    date_part = filename.replace('istanbul_pharmacies_', '').replace('.json', '')

                    # Convert DD_MM_YYYY to datetime
                    file_date = datetime.strptime(date_part, '%d_%m_%Y')

                    # Delete if older than cutoff
                    if file_date < cutoff_date:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"Deleted old file: {filename}")

                except Exception as e:
                    logger.warning(f"Could not process file {file_path}: {e}")
                    continue

            if deleted_count > 0:
                logger.info(f"Cleanup completed. Deleted {deleted_count} old files")
            else:
                logger.info("No old files to delete")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

    def send_email(self, subject, message, is_error=False):
        """Send email notification"""
        if not EMAIL_CONFIG.get('enabled', False):
            return

        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = EMAIL_CONFIG['from_email']
            msg['To'] = ', '.join(EMAIL_CONFIG['to_emails'])
            msg['Subject'] = subject

            # Add body to email
            msg.attach(MIMEText(message, 'plain', 'utf-8'))

            # Create SMTP session
            server = smtplib.SMTP(EMAIL_CONFIG['smtp_server'], EMAIL_CONFIG['smtp_port'])
            server.starttls()  # Enable security
            server.login(EMAIL_CONFIG['username'], EMAIL_CONFIG['password'])

            # Send email
            text = msg.as_string()
            server.sendmail(EMAIL_CONFIG['from_email'], EMAIL_CONFIG['to_emails'], text)
            server.quit()

            logger.info(f"Email sent successfully: {subject}")

        except Exception as e:
            logger.error(f"Failed to send email: {e}")

    def run_istanbul_scrape(self):
        """Run scraping for Istanbul - today and tomorrow"""
        logger.info("Starting Istanbul pharmacy scrape for today and tomorrow")

        # First, cleanup old files (keep only 7 days)
        logger.info("Cleaning up old JSON files...")
        self.cleanup_old_files(days_to_keep=7)

        # Get today and tomorrow dates
        today = datetime.now()
        tomorrow = today + timedelta(days=1)

        dates = [
            today.strftime('%d/%m/%Y'),
            tomorrow.strftime('%d/%m/%Y')
        ]

        total_pharmacies = 0
        daily_results = {}

        for date in dates:
            try:
                logger.info(f"Scraping Istanbul for {date}...")

                # Add small delay between requests
                time.sleep(2)

                pharmacies = self.scrape_pharmacy_data(date=date)
                if pharmacies:
                    self.save_to_json(pharmacies, date)
                    total_pharmacies += len(pharmacies)
                    daily_results[date] = len(pharmacies)
                else:
                    logger.warning(f"No pharmacies found for {date}")
                    daily_results[date] = 0

            except Exception as e:
                logger.error(f"Error processing {date}: {e}")
                daily_results[date] = 0
                continue

        logger.info(f"Istanbul scrape completed. Total pharmacies: {total_pharmacies}")

        # Send success email notification
        if EMAIL_CONFIG.get('enabled', False) and EMAIL_CONFIG.get('send_daily_summary', False):
            subject = f"✅ Istanbul Pharmacy Scraper - Daily Report ({datetime.now().strftime('%d/%m/%Y')})"
            message = f"""Istanbul Pharmacy Scraper Daily Report

📅 Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
🏥 Total Pharmacies Found: {total_pharmacies}
📊 Status: SUCCESS

Details:
- Today ({dates[0]}): {daily_results.get(dates[0], 0)} pharmacies
- Tomorrow ({dates[1]}): {daily_results.get(dates[1], 0)} pharmacies

Files created:
- istanbul_pharmacies_{dates[0].replace('/', '_')}.json
- istanbul_pharmacies_{dates[1].replace('/', '_')}.json

The scraper is working correctly and data has been saved successfully.

---
Istanbul Pharmacy Scraper System
istanbulnobetcieczane.com"""

            self.send_email(subject, message)

        return total_pharmacies

def main():
    """Main function"""
    scraper = IstanbulPharmacyScraper()

    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            # Test with today only
            today = datetime.now().strftime('%d/%m/%Y')
            pharmacies = scraper.scrape_pharmacy_data(date=today)
            print(f"Test completed. Found {len(pharmacies)} pharmacies for today")
            if pharmacies:
                scraper.save_to_json(pharmacies, today)
        elif sys.argv[1] == 'today':
            # Scrape only today
            today = datetime.now().strftime('%d/%m/%Y')
            pharmacies = scraper.scrape_pharmacy_data(date=today)
            if pharmacies:
                scraper.save_to_json(pharmacies, today)
            print(f"Today's scrape completed. Found {len(pharmacies)} pharmacies")
        elif sys.argv[1] == 'tomorrow':
            # Scrape only tomorrow
            tomorrow = (datetime.now() + timedelta(days=1)).strftime('%d/%m/%Y')
            pharmacies = scraper.scrape_pharmacy_data(date=tomorrow)
            if pharmacies:
                scraper.save_to_json(pharmacies, tomorrow)
            print(f"Tomorrow's scrape completed. Found {len(pharmacies)} pharmacies")
        elif sys.argv[1] == 'cleanup':
            # Manual cleanup of old files
            scraper.cleanup_old_files(days_to_keep=7)
            print("Cleanup completed")
        elif sys.argv[1] == 'email-test':
            # Test email functionality
            if EMAIL_CONFIG.get('enabled', False):
                subject = "🧪 Istanbul Pharmacy Scraper - Email Test"
                message = f"""Email Test from Istanbul Pharmacy Scraper

📅 Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
📧 This is a test email to verify email functionality is working correctly.

✅ If you receive this email, the email configuration is working properly!

Configuration:
- SMTP Server: {EMAIL_CONFIG.get('smtp_server', 'Not configured')}
- From Email: {EMAIL_CONFIG.get('from_email', 'Not configured')}
- To Email: {', '.join(EMAIL_CONFIG.get('to_emails', []))}

---
Istanbul Pharmacy Scraper System
istanbulnobetcieczane.com"""

                scraper.send_email(subject, message)
                print("✅ Test email sent! Check your inbox.")
            else:
                print("❌ Email is not enabled in config.py")
        else:
            print("Usage: python3 pharmacy_scraper.py [test|today|tomorrow|cleanup|email-test]")
            print("  test       - Test scraping for today only")
            print("  today      - Scrape today's pharmacies")
            print("  tomorrow   - Scrape tomorrow's pharmacies")
            print("  cleanup    - Remove JSON files older than 7 days")
            print("  email-test - Test email functionality")
            print("  (no args)  - Scrape both today and tomorrow")
    else:
        # Default: scrape both today and tomorrow
        scraper.run_istanbul_scrape()

if __name__ == "__main__":
    main()
