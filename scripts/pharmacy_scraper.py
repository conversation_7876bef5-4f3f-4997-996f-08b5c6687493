#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Turkish Pharmacy Duty Scraper
Scrapes duty pharmacy information from turkiye.gov.tr
"""

import requests
import json
import mysql.connector
import logging
import sys
import os
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import time
import random

# Add the script directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

try:
    from config import DATABASE_CONFIG, SCRAPER_CONFIG
except ImportError:
    print("Error: config.py not found. Please create configuration file.")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(script_dir, 'scraper.log')),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PharmacyScraper:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'tr-TR,tr;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        self.base_url = "https://www.turkiye.gov.tr"
        self.search_url = f"{self.base_url}/saglik-titck-nobetci-eczane-sorgulama"
        
        # Turkish cities list
        self.cities = [
            'ADANA', 'ADIYAMAN', 'AFYONKARAHİSAR', 'AĞRI', 'AKSARAY', 'AMASYA', 
            'ANKARA', 'ANTALYA', 'ARDAHAN', 'ARTVİN', 'AYDIN', 'BALIKESİR', 
            'BARTIN', 'BATMAN', 'BAYBURT', 'BİLECİK', 'BİNGÖL', 'BİTLİS', 
            'BOLU', 'BURDUR', 'BURSA', 'ÇANAKKALE', 'ÇANKIRI', 'ÇORUM', 
            'DENİZLİ', 'DİYARBAKIR', 'DÜZCE', 'EDİRNE', 'ELAZIĞ', 'ERZİNCAN', 
            'ERZURUM', 'ESKİŞEHİR', 'GAZİANTEP', 'GİRESUN', 'GÜMÜŞHANE', 
            'HAKKARİ', 'HATAY', 'IĞDIR', 'ISPARTA', 'İSTANBUL', 'İZMİR', 
            'KAHRAMANMARAŞ', 'KARABÜK', 'KARAMAN', 'KARS', 'KASTAMONU', 
            'KAYSERİ', 'KIRIKKALE', 'KIRKLARELİ', 'KIRŞEHİR', 'KİLİS', 
            'KOCAELİ', 'KONYA', 'KÜTAHYA', 'MALATYA', 'MANİSA', 'MARDİN', 
            'MERSİN', 'MUĞLA', 'MUŞ', 'NEVŞEHİR', 'NİĞDE', 'ORDU', 
            'OSMANİYE', 'RİZE', 'SAKARYA', 'SAMSUN', 'SİİRT', 'SİNOP', 
            'SİVAS', 'ŞANLIURFA', 'ŞIRNAK', 'TEKİRDAĞ', 'TOKAT', 'TRABZON', 
            'TUNCELİ', 'UŞAK', 'VAN', 'YALOVA', 'YOZGAT', 'ZONGULDAK'
        ]

    def connect_database(self):
        """Connect to MySQL database"""
        try:
            connection = mysql.connector.connect(**DATABASE_CONFIG)
            return connection
        except mysql.connector.Error as err:
            logger.error(f"Database connection error: {err}")
            return None

    def get_csrf_token(self):
        """Get CSRF token from the main page"""
        try:
            response = self.session.get(self.search_url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            csrf_token = soup.find('input', {'name': '_token'})
            
            if csrf_token:
                return csrf_token.get('value')
            
            # Alternative: look for meta tag
            csrf_meta = soup.find('meta', {'name': 'csrf-token'})
            if csrf_meta:
                return csrf_meta.get('content')
                
            logger.warning("CSRF token not found")
            return None
            
        except Exception as e:
            logger.error(f"Error getting CSRF token: {e}")
            return None

    def get_districts(self, city_name):
        """Get districts for a given city"""
        try:
            # This would need to be implemented based on the actual API structure
            # For now, return empty list and handle in the main scraping logic
            return []
        except Exception as e:
            logger.error(f"Error getting districts for {city_name}: {e}")
            return []

    def scrape_pharmacy_data(self, city, district=None, date=None):
        """Scrape pharmacy data for given parameters"""
        if date is None:
            date = datetime.now().strftime('%d/%m/%Y')
            
        try:
            # Get CSRF token
            csrf_token = self.get_csrf_token()
            
            # Prepare form data
            form_data = {
                'il': city,
                'tarih': date,
                '_token': csrf_token
            }
            
            if district:
                form_data['ilce'] = district
            
            # Make POST request
            response = self.session.post(
                self.search_url,
                data=form_data,
                timeout=30
            )
            response.raise_for_status()
            
            # Parse response
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract pharmacy data (this will need to be adjusted based on actual HTML structure)
            pharmacies = []
            
            # Look for pharmacy listings in the response
            pharmacy_elements = soup.find_all('div', class_='pharmacy-item')  # Adjust selector
            
            for element in pharmacy_elements:
                pharmacy_data = self.extract_pharmacy_info(element)
                if pharmacy_data:
                    pharmacy_data['city'] = city
                    pharmacy_data['district'] = district
                    pharmacy_data['duty_date'] = date
                    pharmacy_data['scraped_at'] = datetime.now()
                    pharmacies.append(pharmacy_data)
            
            logger.info(f"Found {len(pharmacies)} pharmacies for {city} on {date}")
            return pharmacies
            
        except Exception as e:
            logger.error(f"Error scraping data for {city}: {e}")
            return []

    def extract_pharmacy_info(self, element):
        """Extract pharmacy information from HTML element"""
        try:
            # This needs to be implemented based on actual HTML structure
            # Placeholder implementation
            pharmacy_data = {
                'name': '',
                'address': '',
                'phone': '',
                'district': '',
                'latitude': None,
                'longitude': None
            }
            
            # Extract name
            name_elem = element.find('h3') or element.find('strong')
            if name_elem:
                pharmacy_data['name'] = name_elem.get_text(strip=True)
            
            # Extract address
            address_elem = element.find('p', class_='address')
            if address_elem:
                pharmacy_data['address'] = address_elem.get_text(strip=True)
            
            # Extract phone
            phone_elem = element.find('span', class_='phone')
            if phone_elem:
                pharmacy_data['phone'] = phone_elem.get_text(strip=True)
            
            return pharmacy_data if pharmacy_data['name'] else None
            
        except Exception as e:
            logger.error(f"Error extracting pharmacy info: {e}")
            return None

    def save_to_database(self, pharmacies):
        """Save pharmacy data to database"""
        if not pharmacies:
            return
            
        connection = self.connect_database()
        if not connection:
            return
            
        try:
            cursor = connection.cursor()
            
            # Clear old data for the same date and city
            for pharmacy in pharmacies:
                delete_query = """
                DELETE FROM duty_pharmacies 
                WHERE city = %s AND duty_date = %s
                """
                cursor.execute(delete_query, (pharmacy['city'], pharmacy['duty_date']))
            
            # Insert new data
            insert_query = """
            INSERT INTO duty_pharmacies 
            (name, address, phone, city, district, duty_date, latitude, longitude, scraped_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            for pharmacy in pharmacies:
                cursor.execute(insert_query, (
                    pharmacy['name'],
                    pharmacy['address'],
                    pharmacy['phone'],
                    pharmacy['city'],
                    pharmacy['district'],
                    pharmacy['duty_date'],
                    pharmacy['latitude'],
                    pharmacy['longitude'],
                    pharmacy['scraped_at']
                ))
            
            connection.commit()
            logger.info(f"Saved {len(pharmacies)} pharmacies to database")
            
        except mysql.connector.Error as err:
            logger.error(f"Database error: {err}")
            connection.rollback()
        finally:
            cursor.close()
            connection.close()

    def run_daily_scrape(self):
        """Run daily scraping for all cities"""
        logger.info("Starting daily pharmacy scrape")
        
        total_pharmacies = 0
        today = datetime.now().strftime('%d/%m/%Y')
        
        for city in self.cities:
            try:
                logger.info(f"Scraping {city}...")
                
                # Add random delay to avoid overwhelming the server
                time.sleep(random.uniform(1, 3))
                
                pharmacies = self.scrape_pharmacy_data(city, date=today)
                if pharmacies:
                    self.save_to_database(pharmacies)
                    total_pharmacies += len(pharmacies)
                
            except Exception as e:
                logger.error(f"Error processing {city}: {e}")
                continue
        
        logger.info(f"Daily scrape completed. Total pharmacies: {total_pharmacies}")

def main():
    """Main function"""
    scraper = PharmacyScraper()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'daily':
            scraper.run_daily_scrape()
        elif sys.argv[1] == 'test':
            # Test with Istanbul
            pharmacies = scraper.scrape_pharmacy_data('İSTANBUL')
            print(f"Test completed. Found {len(pharmacies)} pharmacies")
        else:
            print("Usage: python3 pharmacy_scraper.py [daily|test]")
    else:
        scraper.run_daily_scrape()

if __name__ == "__main__":
    main()
