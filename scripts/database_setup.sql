-- Database setup for Turkish Pharmacy Duty Scraper
-- This script creates the necessary tables for storing pharmacy data

-- Use the WordPress database
USE sgkoduyormu_wp_w8bl5;

-- Create duty_pharmacies table
CREATE TABLE IF NOT EXISTS `duty_pharmacies` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `address` text,
    `phone` varchar(50),
    `city` varchar(100) NOT NULL,
    `district` varchar(100),
    `duty_date` date NOT NULL,
    `latitude` decimal(10, 8) DEFAULT NULL,
    `longitude` decimal(11, 8) DEFAULT NULL,
    `scraped_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `is_active` tinyint(1) DEFAULT 1,
    `source_url` varchar(500),
    `additional_info` text,
    PRIMARY KEY (`id`),
    KEY `idx_city_date` (`city`, `duty_date`),
    KEY `idx_duty_date` (`duty_date`),
    KEY `idx_city` (`city`),
    KEY `idx_district` (`district`),
    KEY `idx_scraped_at` (`scraped_at`),
    KEY `idx_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create scraper_logs table for monitoring
CREATE TABLE IF NOT EXISTS `scraper_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `log_level` enum('DEBUG','INFO','WARNING','ERROR','CRITICAL') NOT NULL DEFAULT 'INFO',
    `message` text NOT NULL,
    `city` varchar(100),
    `district` varchar(100),
    `execution_time` decimal(10, 4),
    `records_processed` int(11) DEFAULT 0,
    `records_saved` int(11) DEFAULT 0,
    `error_details` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_log_level` (`log_level`),
    KEY `idx_city` (`city`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create scraper_statistics table for tracking performance
CREATE TABLE IF NOT EXISTS `scraper_statistics` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `scrape_date` date NOT NULL,
    `total_cities_processed` int(11) DEFAULT 0,
    `total_pharmacies_found` int(11) DEFAULT 0,
    `total_pharmacies_saved` int(11) DEFAULT 0,
    `execution_start_time` timestamp NULL,
    `execution_end_time` timestamp NULL,
    `execution_duration` decimal(10, 4),
    `errors_count` int(11) DEFAULT 0,
    `success_rate` decimal(5, 2),
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_scrape_date` (`scrape_date`),
    KEY `idx_scrape_date` (`scrape_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create pharmacy_cache table for faster lookups
CREATE TABLE IF NOT EXISTS `pharmacy_cache` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `cache_key` varchar(255) NOT NULL,
    `cache_data` longtext,
    `city` varchar(100),
    `district` varchar(100),
    `cache_date` date,
    `expires_at` timestamp NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_cache_key` (`cache_key`),
    KEY `idx_city_district_date` (`city`, `district`, `cache_date`),
    KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create cities table for reference
CREATE TABLE IF NOT EXISTS `cities` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `code` varchar(10),
    `plate_number` varchar(3),
    `region` varchar(50),
    `is_active` tinyint(1) DEFAULT 1,
    `priority` int(11) DEFAULT 0,
    `last_scraped` timestamp NULL,
    `scrape_frequency_hours` int(11) DEFAULT 24,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_name` (`name`),
    UNIQUE KEY `unique_code` (`code`),
    KEY `idx_active` (`is_active`),
    KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create districts table for reference
CREATE TABLE IF NOT EXISTS `districts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `city_id` int(11) NOT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`id`),
    KEY `idx_city_id` (`city_id`),
    KEY `idx_active` (`is_active`),
    FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert Turkish cities data
INSERT INTO `cities` (`name`, `code`, `plate_number`, `region`, `priority`) VALUES
('ADANA', 'ADANA', '01', 'Akdeniz', 5),
('ADIYAMAN', 'ADIYAMAN', '02', 'Güneydoğu Anadolu', 0),
('AFYONKARAHİSAR', 'AFYONKARAHISAR', '03', 'Ege', 0),
('AĞRI', 'AGRI', '04', 'Doğu Anadolu', 0),
('AKSARAY', 'AKSARAY', '68', 'İç Anadolu', 0),
('AMASYA', 'AMASYA', '05', 'Karadeniz', 0),
('ANKARA', 'ANKARA', '06', 'İç Anadolu', 10),
('ANTALYA', 'ANTALYA', '07', 'Akdeniz', 8),
('ARDAHAN', 'ARDAHAN', '75', 'Doğu Anadolu', 0),
('ARTVİN', 'ARTVIN', '08', 'Karadeniz', 0),
('AYDIN', 'AYDIN', '09', 'Ege', 3),
('BALIKESİR', 'BALIKESIR', '10', 'Marmara', 3),
('BARTIN', 'BARTIN', '74', 'Karadeniz', 0),
('BATMAN', 'BATMAN', '72', 'Güneydoğu Anadolu', 0),
('BAYBURT', 'BAYBURT', '69', 'Karadeniz', 0),
('BİLECİK', 'BILECIK', '11', 'Marmara', 0),
('BİNGÖL', 'BINGOL', '12', 'Doğu Anadolu', 0),
('BİTLİS', 'BITLIS', '13', 'Doğu Anadolu', 0),
('BOLU', 'BOLU', '14', 'Karadeniz', 0),
('BURDUR', 'BURDUR', '15', 'Akdeniz', 0),
('BURSA', 'BURSA', '16', 'Marmara', 8),
('ÇANAKKALE', 'CANAKKALE', '17', 'Marmara', 2),
('ÇANKIRI', 'CANKIRI', '18', 'İç Anadolu', 0),
('ÇORUM', 'CORUM', '19', 'Karadeniz', 0),
('DENİZLİ', 'DENIZLI', '20', 'Ege', 3),
('DİYARBAKIR', 'DIYARBAKIR', '21', 'Güneydoğu Anadolu', 5),
('DÜZCE', 'DUZCE', '81', 'Karadeniz', 0),
('EDİRNE', 'EDIRNE', '22', 'Marmara', 2),
('ELAZIĞ', 'ELAZIG', '23', 'Doğu Anadolu', 0),
('ERZİNCAN', 'ERZINCAN', '24', 'Doğu Anadolu', 0),
('ERZURUM', 'ERZURUM', '25', 'Doğu Anadolu', 2),
('ESKİŞEHİR', 'ESKISEHIR', '26', 'İç Anadolu', 3),
('GAZİANTEP', 'GAZIANTEP', '27', 'Güneydoğu Anadolu', 6),
('GİRESUN', 'GIRESUN', '28', 'Karadeniz', 0),
('GÜMÜŞHANE', 'GUMUSHANE', '29', 'Karadeniz', 0),
('HAKKARİ', 'HAKKARI', '30', 'Doğu Anadolu', 0),
('HATAY', 'HATAY', '31', 'Akdeniz', 3),
('IĞDIR', 'IGDIR', '76', 'Doğu Anadolu', 0),
('ISPARTA', 'ISPARTA', '32', 'Akdeniz', 0),
('İSTANBUL', 'ISTANBUL', '34', 'Marmara', 10),
('İZMİR', 'IZMIR', '35', 'Ege', 9),
('KAHRAMANMARAŞ', 'KAHRAMANMARAS', '46', 'Akdeniz', 2),
('KARABÜK', 'KARABUK', '78', 'Karadeniz', 0),
('KARAMAN', 'KARAMAN', '70', 'İç Anadolu', 0),
('KARS', 'KARS', '36', 'Doğu Anadolu', 0),
('KASTAMONU', 'KASTAMONU', '37', 'Karadeniz', 0),
('KAYSERİ', 'KAYSERI', '38', 'İç Anadolu', 3),
('KIRIKKALE', 'KIRIKKALE', '71', 'İç Anadolu', 0),
('KIRKLARELİ', 'KIRKLARELI', '39', 'Marmara', 0),
('KIRŞEHİR', 'KIRSEHIR', '40', 'İç Anadolu', 0),
('KİLİS', 'KILIS', '79', 'Güneydoğu Anadolu', 0),
('KOCAELİ', 'KOCAELI', '41', 'Marmara', 4),
('KONYA', 'KONYA', '42', 'İç Anadolu', 6),
('KÜTAHYA', 'KUTAHYA', '43', 'Ege', 0),
('MALATYA', 'MALATYA', '44', 'Doğu Anadolu', 2),
('MANİSA', 'MANISA', '45', 'Ege', 3),
('MARDİN', 'MARDIN', '47', 'Güneydoğu Anadolu', 0),
('MERSİN', 'MERSIN', '33', 'Akdeniz', 5),
('MUĞLA', 'MUGLA', '48', 'Ege', 3),
('MUŞ', 'MUS', '49', 'Doğu Anadolu', 0),
('NEVŞEHİR', 'NEVSEHIR', '50', 'İç Anadolu', 0),
('NİĞDE', 'NIGDE', '51', 'İç Anadolu', 0),
('ORDU', 'ORDU', '52', 'Karadeniz', 0),
('OSMANİYE', 'OSMANIYE', '80', 'Akdeniz', 0),
('RİZE', 'RIZE', '53', 'Karadeniz', 0),
('SAKARYA', 'SAKARYA', '54', 'Marmara', 2),
('SAMSUN', 'SAMSUN', '55', 'Karadeniz', 3),
('SİİRT', 'SIIRT', '56', 'Güneydoğu Anadolu', 0),
('SİNOP', 'SINOP', '57', 'Karadeniz', 0),
('SİVAS', 'SIVAS', '58', 'İç Anadolu', 0),
('ŞANLIURFA', 'SANLIURFA', '63', 'Güneydoğu Anadolu', 2),
('ŞIRNAK', 'SIRNAK', '73', 'Güneydoğu Anadolu', 0),
('TEKİRDAĞ', 'TEKIRDAG', '59', 'Marmara', 2),
('TOKAT', 'TOKAT', '60', 'Karadeniz', 0),
('TRABZON', 'TRABZON', '61', 'Karadeniz', 2),
('TUNCELİ', 'TUNCELI', '62', 'Doğu Anadolu', 0),
('UŞAK', 'USAK', '64', 'Ege', 0),
('VAN', 'VAN', '65', 'Doğu Anadolu', 0),
('YALOVA', 'YALOVA', '77', 'Marmara', 0),
('YOZGAT', 'YOZGAT', '66', 'İç Anadolu', 0),
('ZONGULDAK', 'ZONGULDAK', '67', 'Karadeniz', 0);

-- Create indexes for better performance
CREATE INDEX idx_duty_pharmacies_city_date ON duty_pharmacies(city, duty_date);
CREATE INDEX idx_duty_pharmacies_name ON duty_pharmacies(name);
CREATE INDEX idx_scraper_logs_level_date ON scraper_logs(log_level, created_at);

-- Create a view for current duty pharmacies (today's data)
CREATE OR REPLACE VIEW current_duty_pharmacies AS
SELECT 
    dp.*,
    c.region,
    c.plate_number
FROM duty_pharmacies dp
LEFT JOIN cities c ON dp.city = c.name
WHERE dp.duty_date = CURDATE()
AND dp.is_active = 1
ORDER BY dp.city, dp.district, dp.name;

-- Create a view for pharmacy statistics
CREATE OR REPLACE VIEW pharmacy_statistics AS
SELECT 
    city,
    COUNT(*) as total_pharmacies,
    COUNT(DISTINCT duty_date) as days_covered,
    MIN(duty_date) as first_date,
    MAX(duty_date) as last_date,
    MAX(scraped_at) as last_updated
FROM duty_pharmacies 
WHERE is_active = 1
GROUP BY city
ORDER BY total_pharmacies DESC;
